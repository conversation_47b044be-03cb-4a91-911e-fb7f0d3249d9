<template>
  <div class="d-flex justify-center">
    <div style="max-width: 960px">
      <OrderFilter
        ref="orderFilterRef"
        :payment-method-list="payment_method_list"
        v-model:filters="filters_order"
        v-model:selected-mode="selectedMode"
        :pre-order-amount="pre_order_amount"
        :pre-order-to-ship-amount="pre_order_to_ship_amount"
      />

      <v-infinite-scroll @load="loadMoreOrder">
        <template v-for="(order, index) in orders" :key="index">
          <OrderCard
            :order="order"
            :selected-mode="selectedMode"
            class="mb-1"
            @update_order_status="updateOrder(index, $event)"
          />
        </template>

        <template v-slot:load-more="{ props }">
          <v-btn
            icon="mdi-refresh"
            size="small"
            variant="text"
            v-bind="props"
          ></v-btn>
        </template>
      </v-infinite-scroll>
    </div>
  </div>
</template>

<script setup lang="ts">
definePageMeta({
  permission: "full_access_order",
});
import moment from "moment";
import { useMainStore } from "~/store";
const store = useMainStore();
const nuxtApp = useNuxtApp();
const route = useRoute();
const _ = useLodash();
import { Order, FiltersOrder } from "~/interface/orders";

const { $loader } = useNuxtApp();
const { sfetch } = useServerFetch();
const payment_method_list = ref([]);
const orderFilterRef = ref();
const orders = ref<Order[]>([]);
const selectedMode = ref("ordinary_admin");

const filters_order = ref<FiltersOrder>({
  search: "",
  start_date: moment().format("YYYY-MM-DD"),
  end_date: moment().format("YYYY-MM-DD"),
  order_status: "",
  order_type: "",
  payment_status: null,
  payment_method: "",
  is_pre_order: false,
  tab_status: "ALL",
});

let current_page = 1;
let max_page = 1;
let pre_order_amount = ref(0);
let pre_order_to_ship_amount = ref(0);

//  await getOrder(newValue)
watch(filters_order, async (newValue) => {
  orders.value = [];
  current_page = 1;
  max_page = 1;
  debounceGetOrders();
  debounceGetOrderToShipAmount();
});

watch(selectedMode, async (newValue) => {
  orders.value = [];
  current_page = 1;
  max_page = 1;
  debounceGetOrders();
  debounceGetOrderToShipAmount();
});

const debounceGetOrders = _.debounce(getOrder, 500);
const debounceGetOrderToShipAmount = _.debounce(fetchOrderToShipAmount, 500);

async function fetchOrderToShipAmount() {
  pre_order_to_ship_amount.value = await getPreOrderToShipAmount();
}

onMounted(async () => {
  nuxtApp.$store.setCheckout(false);

  if (route.query.unpaid) {
    filters_order.value.payment_status = false;
    filters_order.value.start_date = "";
    orderFilterRef.value.toggleAdvanceFilter();
  }

  await getOrder();
  pre_order_amount.value = await getPreOrderAmount();
  pre_order_to_ship_amount.value = await getPreOrderToShipAmount();
  await getPaymentMethod();
});

let vdone: any = null;
async function loadMoreOrder({ done }: any) {
  if (!vdone) {
    vdone = done;
  }
  if (current_page >= max_page) {
    done("empty");
    return;
  }
  current_page += 1;
  await getOrder();
  done("ok");
}

async function getOrder(): Promise<void> {
  $loader(true);
  try {
    const filters = filters_order.value;

    let params = {} as any;

    const date_g = filters.start_date
      ? `${filters.start_date}T00:00:00`
      : undefined;
    const date_l = filters.end_date
      ? `${filters.end_date}T23:59:59`
      : undefined;
    if (filters.tab_status == "TO_SHIP") {
      params["filter{pickup_datetime.gte}"] = date_g;
      params["filter{pickup_datetime.lte}"] = date_l;
    } else if (filters.tab_status == "ALL") {
      params["filter{order_datetime.gte}"] = date_g;
      params["filter{order_datetime.lte}"] = date_l;
    }

    if (filters.tab_status == "PRE_ORDER") {
      params["filter{order_status}"] = "WAITING_FOR_PICKUP";
    } else {
      params["filter{order_status}"] = filters.order_status || undefined;
    }

    if (filters.payment_status !== null) {
      params["filter{payment_status}"] = filters.payment_status;
    }

    if (selectedMode.value == "ordinary_admin") {
      params["involved_branch"] = store.branch_id;
    }

    if (selectedMode.value == "ecommerce_admin") {
      const saleChannelId = store.company.settings?.ECOMMERCE_SALE_CHANNEL;
      params["filter{sale_channel}"] = saleChannelId;
    }

    const data = (await sfetch(`/order/resource/orders/`, {
      params: {
        ...params,
        page: current_page,
        per_page: 15,
        "include[]": [
          "sale_channel_set.image",
          "payment_method_set.image",
          "shipping_set.delivery_status",
          "pickup_branch_set.name",
        ],
        "exclude[]": [
          "order_items",
          'sale_channel_set.*',
          'shipping_set.*',
          'pickup_branch_set.*',
          'payment_method_set.*',
        ],
        general_search: filters.search,
        place__type: filters.order_type,
        "sort[]":
          filters.tab_status != "ALL"
            ? ["-order_status", "pickup_datetime"]
            : "-order_datetime",
        "filter{company}": getCompanyID(),
        "filter{payment_method}": filters.payment_method || undefined,
        "filter{is_pre_order}": filters.is_pre_order || undefined,
      },
    })) as any;

    max_page = data.meta.total_pages;
    if (current_page == 1 && vdone) {
      vdone("ok");
    }
    orders.value.push(...data.orders);
  } catch (e) {
    const error = e as Error;
    alertHttpError(error);
  }
  $loader(false);
}

async function getPreOrderAmount() {
  try {
    const data = (await sfetch(`/order/resource/orders/`, {
      params: {
        "exclude[]": ["*"],
        "filter{is_pre_order}": true,
        "filter{order_status}": "WAITING_FOR_PICKUP",
        "involved_branch": store.branch_id,
        per_page: 1,
      },
    })) as any;

    return data.meta.total_results;
  } catch (error) {
    alertHttpError(error);
  }
}

async function getPreOrderToShipAmount() {
  try {
    const date_g = filters_order.value.start_date
      ? `${filters_order.value.start_date}T00:00:00Z`
      : undefined;
    const date_l = filters_order.value.end_date
      ? `${filters_order.value.end_date}T23:59:59Z`
      : undefined;

    const data = (await sfetch(`/order/resource/orders/`, {
      params: {
        "exclude[]": ["*"],
        "filter{is_pre_order}": true,
        "filter{order_status}": "WAITING_FOR_PICKUP",
        "involved_branch": store.branch_id,
        per_page: 1,
        "filter{pickup_datetime.gte}": date_g,
        "filter{pickup_datetime.lte}": date_l,
      },
    })) as any;

    return data.meta.total_results;
  } catch (error) {
    alertHttpError(error);
  }
}

function updateOrder(index, status) {
  orders.value[index].order_status = status;
}

async function getPaymentMethod() {
  try {
    const response = await sfetch("/order/payment/list/");
    payment_method_list.value = response;
  } catch (error) {
    alertHttpError(error);
  }
}
</script>
