<template>
  <!-- density="comfortable" -->
  <v-card elevation="0" class="w-100 border" :to="`/order/${order.id}/detail2`">
    <div class="d-flex flex-wrap">
      <!-- Image -->
      <!-- v-if="smAndUp" -->
      <div style="width: 40px" class="d-flex align-center ma-2 mb-xs-0">
        <v-img
          aspect-ratio="1"
          v-if="order.sale_channel_set"
          class="rounded"
          cover
          :src="
            order.sale_channel_set.image ||
            'https://user-images.githubusercontent.com/24848110/33519396-7e56363c-d79d-11e7-969b-09782f5ccbab.png'
          "
        ></v-img>
        <v-img
          v-else
          aspect-ratio="1"
          class="rounded-lg bg-grey-lighten-1 text-center d-flex align-center justify-center text-caption"
          width="100%"
        >
          ไม่ระบุช่องทางขาย
        </v-img>
      </div>
      <!-- Customer Detail -->
      <div class="mt-1">
        <h4># {{ props.order.order_number }}</h4>
        <p class="text-grey-darken-2 text-caption text-sm-body-2">
          <span
            class="text-black font-weight-bold"
            v-if="order.customer_detail.name"
          >
            {{ order.customer_detail.name || "" }}
            |</span
          >
          {{
            order.order_datetime
              ? moment(order.order_datetime).format("DD/MM/YY HH:mm")
              : "-"
          }}

          <span v-if="order.pickup_branch_set">
            <span class="text-black font-weight-bold"> | </span>
            ส่งโดย:
            <span class="text-black font-weight-bold"
              >{{ order?.pickup_branch_set?.name  }}
            </span>
          </span>
        </p>
      </div>
      <v-spacer></v-spacer>
      <!-- for mobile -->
      <div class="d-flex align-center flex-wrap pa-1" style="max-width: 100%">
        <v-chip
          class="ma-1"
          label
          color="warning"
          prepend-icon="mdi-alert"
          v-if="!order.drawer && order.branch == store.branch_id"
          >รับเข้ากะ
        </v-chip>
        <v-chip
          class="ma-1"
          label
          prepend-icon="mdi-clock-fast"
          variant="tonal"
          :color="preOrderColor(order)"
          v-if="order.pickup_datetime"
        >
          <div class="text-center" style="line-height: 0.8rem">
            <p>
              {{ moment(order.pickup_datetime).format("DD/MM/YY") }}
            </p>
            <p>
              {{ time(order.pickup_datetime) }}
            </p>
          </div>
        </v-chip>
        <!-- place detail -->
        <v-chip class="ma-1" label>
          <v-icon size="20" style="font-weight: bold">
            {{ getPlaceIcon(order) }}
          </v-icon>
          <span v-if="order.place.type == 'dinein'" class="ms-2">
            {{ order.place.detail.table }}
          </span>
        </v-chip>

        <!-- delivery statys -->
        <order-delivery-status
          class="ma-1"
          label
          v-if="order.shipping_set"
          prepend-icon="mdi-truck-fast-outline"
          :status="order.shipping_set?.delivery_status"
        />

        <v-chip
          :height="25"
          :width="60"
          variant="flat"
          label
          :color="status[order.order_status].color"
          :prepend-icon="status[order.order_status].icon"
          @click.prevent="updateOrderStatus"
          class="ma-1"
        >
          <span>{{ status[order.order_status].name }}</span>
        </v-chip>

        <v-chip
          label
          class="ma-1"
          :color="
            order.payment_status
              ? 'success'
              : order.is_cod
              ? 'yellow-darken-2'
              : 'deep-orange-accent-3'
          "
        >
          <template #prepend>
            <span
              v-if="
                order.payment_detail?.type == 'static_qr_code' &&
                order.payment_detail?.payment_slip &&
                !order.payment_status
              "
              class="me-2 font-weight-medium"
            >
              <v-icon>mdi-qrcode </v-icon>
              <span v-if="is_new_uploaded"> รอยืนยัน </span>
              <span v-else> รอแก้ไขหลักฐาน </span>
            </span>
            <v-avatar
              v-else-if="order.payment_method_set"
              class="me-2"
              size="20px"
              rounded="lg"
              tile
            >
              <v-img :src="order.payment_method_set?.image" />
            </v-avatar>
          </template>

          {{ thaiCurrency(order.total_price, 0) }}
        </v-chip>
      </div>
    </div>
  </v-card>
</template>

<script setup lang="ts">
import { Order } from "~/interface/orders";
import { useMainStore } from "~/store";

const store = useMainStore();
const { time,  moment } = useFormatter();
const { sfetch } = useServerFetch();
const props = defineProps({
  order: {
    type: Object as PropType<Order>,
    required: true,
  },
  selectedMode: {
    type: String,
    required: true,
  },
  max_width: String,
});
const emit = defineEmits(["update_order_status"]);

const place: any = {
  dinein: "mdi-table-chair",
  take_away: "mdi-home-variant-outline",
  delivery: "mdi-bicycle",
  platform: "mdi-cellphone-text",
  branch: "mdi-store-marker",
  pre_order: "mdi-book-clock-outline",
};

const status = {
  NEW: {
    icon: "mdi-clipboard-outline",
    color: "light-blue-accent-1",
    name: "ใหม่",
  },
  PENDING: {
    icon: "mdi-clipboard-clock-outline",
    color: "yellow-lighten-3",
    name: "รอรับ",
  },
  COOKING: {
    icon: "mdi-clipboard-clock-outline",
    color: "orange-accent-1",
    name: "กำลังทำ",
  },
  COOKED: {
    icon: "mdi-clipboard-clock-outline",
    color: "orange-accent-1",
    name: "รอส่ง",
  },
  WAITING_FOR_PICKUP: {
    icon: "mdi-clipboard-clock-outline",
    color: "orange-accent-1",
    name: "รอรับ",
  },
  ON_DELIVERY: {
    icon: "mdi-moped",
    color: "teal-accent-1",
    name: "จัดส่ง",
  },
  FINISHED: {
    icon: "mdi-clipboard-check-outline",
    color: "light-green-lighten-3",
    name: "เสร็จสิ้น",
  },
  CANCELLED: {
    icon: "mdi-clipboard-remove-outline",
    color: "deep-orange-accent-1",
    name: "ยกเลิก",
  },
} as any;

const is_new_uploaded = computed(() => {
  const uploaded_datetime =
    props.order.payment_detail?.payment_slip_uploaded_datetime;
  const reject_datetime =
    props.order.payment_detail?.reject_payment_slip_datetime;
  if (uploaded_datetime && reject_datetime) {
    if (moment(reject_datetime).isAfter(uploaded_datetime)) {
      return false;
    } else {
      return true;
    }
  } else if (uploaded_datetime) {
    return true;
  }
  return false;
});

async function updateOrderStatus() {
  let new_order_status = "";
  if (props.order.order_status == "COOKED") {
    new_order_status = "ON_DELIVERY";
  }
  if (props.order.order_status == "ON_DELIVERY") {
    new_order_status = "FINISHED";
  }
  if (new_order_status) {
    if (
      !confirm(`ยืนยันการเปลี่ยนสถานะเป็น ${status[new_order_status].name}`)
    ) {
      return;
    }

    await sfetch(`/order/resource/orders/${props.order.id}/`, {
      method: "PATCH",
      body: {
        order_status: new_order_status,
      },
    });
    emit("update_order_status", new_order_status);
    // $alertCard({
    //   text: "อัพเดทสถานะเรียบร้อย",
    //   theme: "success",
    // })
  }
}

// <v-icon v-if="" :size="20" style="font-weight: bold">
//   mdi-close
// </v-icon>
// <v-icon
//   v-else-if="order.place.type == 'dinein'"
//   class="me-3"
//   :size="18"
// >
//   {{ place[order.place.type] }}
// </v-icon>
// <v-icon v-else :size="20">{{ place[order.place.type] }} </v-icon>
const getPlaceIcon = (order: Order) => {
  if (order.order_status == "CANCELLED") {
    return "mdi-close";
  }

  return place[order.place.type];
};

const preOrderColor = (order: Order) => {
  if (order.actual_receiving_datetime) {
    return "success";
  }

  if (moment(order.pickup_datetime).isBefore(moment().add(12, "hour"))) {
    return "red";
  }
  if (moment(order.pickup_datetime).isBefore(moment().add(24, "hour"))) {
    return "warning";
  }
};
</script>
